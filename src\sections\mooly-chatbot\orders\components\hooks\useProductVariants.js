'use client';

import { useMemo, useState, useCallback } from 'react';

import { isVariableProduct } from 'src/actions/mooly-chatbot/product-constants';
import { normalizeFieldData } from '../orderFieldConfig';
import { getDisplayPrice } from 'src/utils/product-price-utils';


/**
 * Custom hook để quản lý product variants
 */
export function useProductVariants() {
  const [productVariants, setProductVariants] = useState({});
  const [additionalVariants, setAdditionalVariants] = useState({});
  const [isLoadingVariants, setIsLoadingVariants] = useState(false);

  // Kết hợp variants từ API và additional variants
  const allProductVariants = useMemo(() => {
    const combined = { ...productVariants };

    Object.keys(additionalVariants).forEach(productId => {
      if (combined[productId]) {
        // Kết hợp với variants hiện có
        const existingVariants = combined[productId];
        const additionalVars = additionalVariants[productId];

        additionalVars.forEach(variant => {
          const exists = existingVariants.find(v => v.id === variant.id);
          if (!exists) {
            existingVariants.push(variant);
          }
        });
      } else {
        // Thêm mới
        combined[productId] = additionalVariants[productId];
      }
    });

    return combined;
  }, [productVariants, additionalVariants]);

  // Load variants cho một sản phẩm
  const loadProductVariants = useCallback(async (productId, product = null) => {
    console.log('🔍 loadProductVariants called:', { productId, product: product?.name, type: product?.type });

    if (!productId) {
      console.log('❌ No productId provided');
      return [];
    }

    // Kiểm tra nếu đã load rồi
    if (productId in allProductVariants) {
      console.log('✅ Variants already loaded for product:', productId, allProductVariants[productId]);
      return allProductVariants[productId];
    }

    // Kiểm tra nếu sản phẩm không có variants
    if (product && !isVariableProduct(product)) {
      console.log('❌ Product is not variable:', { type: product.type, isVariable: isVariableProduct(product) });
      // Set empty array để đánh dấu đã load
      setProductVariants(prev => ({
        ...prev,
        [productId]: []
      }));
      return [];
    }

    console.log('🚀 Loading variants for variable product:', productId);

    try {
      setIsLoadingVariants(true);

      // Dynamic import để tránh circular dependency
      const { getProductVariants } = await import('src/actions/mooly-chatbot/product-service');
      console.log('📡 Calling getProductVariants API for:', productId);
      const result = await getProductVariants(productId);
      console.log('📡 API response:', result);

      if (result.success && result.data && Array.isArray(result.data)) {
        const normalizedVariants = normalizeFieldData(result.data, 'variant');
        console.log('✅ Variants loaded successfully:', {
          productId,
          rawVariants: result.data,
          normalizedVariants
        });

        setProductVariants(prev => ({
          ...prev,
          [productId]: normalizedVariants
        }));

        return normalizedVariants;
      } else {
        console.log('❌ No variants found or API error:', result);
        // Set empty array để đánh dấu đã load
        setProductVariants(prev => ({
          ...prev,
          [productId]: []
        }));
        return [];
      }
    } catch (error) {
      console.error(`❌ Error loading variants for product ${productId}:`, error);
      // Set empty array để đánh dấu đã load
      setProductVariants(prev => ({
        ...prev,
        [productId]: []
      }));
      return [];
    } finally {
      setIsLoadingVariants(false);
    }
  }, [allProductVariants]);

  // Auto-load variants khi chọn sản phẩm
  const autoLoadVariants = useCallback(async (orderItems) => {
    if (!orderItems || !Array.isArray(orderItems)) return;

    const loadPromises = [];

    for (const item of orderItems) {
      if (item?.productId && item?.product) {
        const product = item.product;
        const productId = product.id;

        // Chỉ load nếu chưa có trong cache và sản phẩm có variants
        if (isVariableProduct(product) && !(productId in allProductVariants)) {
          loadPromises.push(loadProductVariants(productId, product));
        }
      }
    }

    // Load parallel để tối ưu performance
    if (loadPromises.length > 0) {
      await Promise.all(loadPromises);
    }
  }, [allProductVariants, loadProductVariants]);

  // Handle product change và auto-load variants
  const handleProductChange = useCallback(async (index, product, setValue) => {
    console.log('🔍 handleProductChange called:', { index, product: product?.name, type: product?.type });

    if (!product) {
      console.log('❌ No product selected, resetting fields');
      // Reset tất cả thông tin khi bỏ chọn sản phẩm
      setValue(`orderItems.${index}.productId`, '');
      setValue(`orderItems.${index}.product`, null);
      setValue(`orderItems.${index}.name`, '');
      setValue(`orderItems.${index}.sku`, '');
      setValue(`orderItems.${index}.unitPrice`, 0);
      setValue(`orderItems.${index}.totalPrice`, 0);
      setValue(`orderItems.${index}.avatar`, '');
      setValue(`orderItems.${index}.variantId`, '');
      setValue(`orderItems.${index}.variantDetail`, null);
      setValue(`orderItems.${index}.variantInfo`, null);
      setValue(`orderItems.${index}.variant`, null);
      return;
    }

    console.log('✅ Product selected:', {
      id: product.id,
      name: product.name,
      type: product.type,
      isVariable: isVariableProduct(product)
    });

    // Cập nhật thông tin sản phẩm
    setValue(`orderItems.${index}.productId`, product.id);
    setValue(`orderItems.${index}.product`, product);
    setValue(`orderItems.${index}.name`, product.name);
    setValue(`orderItems.${index}.sku`, product.sku || '');
    setValue(`orderItems.${index}.avatar`, product.avatar || '');

    // Sử dụng utility function để lấy giá hiển thị
    const productPrice = getDisplayPrice(product);
    setValue(`orderItems.${index}.unitPrice`, productPrice);

    // Tính tổng tiền
    const quantity = 1; // Default quantity
    setValue(`orderItems.${index}.quantity`, quantity);
    setValue(`orderItems.${index}.totalPrice`, productPrice * quantity);

    // Reset variant khi chọn sản phẩm mới
    setValue(`orderItems.${index}.variantId`, '');
    setValue(`orderItems.${index}.variantDetail`, null);
    setValue(`orderItems.${index}.variantInfo`, null);
    setValue(`orderItems.${index}.variant`, null);

    // Auto-load variants nếu sản phẩm có variants
    if (isVariableProduct(product)) {
      console.log('🚀 Product has variants, loading them...');
      await loadProductVariants(product.id, product);
    } else {
      console.log('ℹ️ Product is simple, no variants to load');
    }
  }, [loadProductVariants]);

  // Handle variant change
  const handleVariantChange = useCallback((index, variant, setValue, orderItems) => {
    if (!variant) {
      // Reset variant info
      setValue(`orderItems.${index}.variantId`, '');
      setValue(`orderItems.${index}.variantDetail`, null);
      setValue(`orderItems.${index}.variantInfo`, null);
      setValue(`orderItems.${index}.variant`, null);

      // Quay lại giá của sản phẩm chính
      const productDetail = orderItems[index]?.productDetail || orderItems[index]?.product;
      if (productDetail) {
        const productPrice = getDisplayPrice(productDetail);
        setValue(`orderItems.${index}.unitPrice`, productPrice);
        setValue(`orderItems.${index}.sku`, productDetail.sku || '');

        if (productDetail.avatar) {
          setValue(`orderItems.${index}.avatar`, productDetail.avatar);
        }

        // Tính lại tổng tiền
        const quantity = orderItems[index]?.quantity || 1;
        setValue(`orderItems.${index}.totalPrice`, productPrice * quantity);
      }
      return;
    }

    // Set variant info
    setValue(`orderItems.${index}.variantId`, variant.id);
    setValue(`orderItems.${index}.variantDetail`, variant);
    setValue(`orderItems.${index}.variant`, variant);
    setValue(`orderItems.${index}.sku`, variant.sku || '');
    setValue(`orderItems.${index}.variantInfo`, variant.attributes || null);

    // Sử dụng utility function để lấy giá hiển thị của variant
    const variantPrice = getDisplayPrice(variant);
    setValue(`orderItems.${index}.unitPrice`, variantPrice);

    // Tính tổng tiền
    const quantity = orderItems[index]?.quantity || 1;
    setValue(`orderItems.${index}.totalPrice`, variantPrice * quantity);

    // Sử dụng hình ảnh của variant nếu có
    if (variant.avatar) {
      setValue(`orderItems.${index}.avatar`, variant.avatar);
    }
  }, []);

  // Tìm variant theo ID
  const findVariantById = useCallback((variantId, productId) => {
    if (!variantId || !productId || !allProductVariants[productId]) return null;
    return allProductVariants[productId].find(v => v.id === variantId) || null;
  }, [allProductVariants]);

  // Add additional variants (cho edit mode)
  const addAdditionalVariants = useCallback((productId, variants) => {
    if (!productId || !variants || !Array.isArray(variants)) return;

    const normalizedVariants = normalizeFieldData(variants, 'variant');

    setAdditionalVariants(prev => ({
      ...prev,
      [productId]: normalizedVariants
    }));
  }, []);

  return {
    // State
    productVariants,
    allProductVariants,
    isLoadingVariants,

    // Methods
    loadProductVariants,
    autoLoadVariants,
    handleProductChange,
    handleVariantChange,
    findVariantById,
    addAdditionalVariants,

    // Setters
    setProductVariants,
    setAdditionalVariants,
  };
}
